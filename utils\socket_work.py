from PySide2.QtCore import QThread, Signal, QMutex, QMutexLocker
import socket
import time
import math
from utils.cirular_queue import CircularQueue
from . import logger  # 從同一個包導入 logger

class SocketWorker(QThread):

    # raw_data_received = Signal(str)  # 當收到數據時發出 Signal
    raw_data_received = Signal(str)  # 發送原始數據信號
    sig_socket_connect = Signal(object)  # 發送 Socket 信號

    def __init__(self, host='*************', port=1333,sampleRate=10000):

        super().__init__()
        self.host = host
        self.port = port
        self.SampleRate = sampleRate
        self.SamplePoint1 = math.ceil(self.SampleRate / 12.5)
        self.sample_N = math.ceil(self.SamplePoint1 * 1.005)
        self.sample_byte = int(self.sample_N * 16)

        self.running = False
        self.paused = False
        self.sock = None
        self.connected = False  # 新增：連線狀態標記
        self.connection_mutex = QMutex()  # 新增：連線操作互斥鎖
        self.force_disconnect = False  # 新增：強制斷線標記

        self.collect_data = ""
        self.temp_data = ""

    def run(self):
        self.running = True
        connection_attempts = 0
        max_connection_attempts = 3

        while self.running and not self.force_disconnect:
            try:
                # 檢查是否需要連線
                if not self.connected and not self.paused:
                    if connection_attempts >= max_connection_attempts:
                        logger.error(f"連線失敗超過 {max_connection_attempts} 次，停止嘗試")
                        break

                    try:
                        self._connect_socket()
                        connection_attempts = 0  # 重置連線嘗試次數
                    except Exception as e:
                        connection_attempts += 1
                        logger.error(f"連線失敗 (嘗試 {connection_attempts}/{max_connection_attempts}): {e}")
                        time.sleep(2)  # 等待後重試
                        continue

                # 主要數據接收迴圈
                while self.running and self.connected and not self.force_disconnect:
                    if self.paused:
                        # ✅ 暫停時把接收緩衝區清空（讀掉但丟棄）
                        self._drain_socket()
                        # 也清掉我們自己暫存的切片
                        self.temp_data = ""
                        self.collect_data = ""
                        time.sleep(0.2)
                        continue

                    if self.sock is None:
                        logger.warning("Socket 為 None，中斷連線")
                        break

                    try:
                        hex_data = self.receive_chunk(self.sample_N, self.sample_byte)
                        if hex_data:
                            self.emit_data(hex_data)
                    except socket.timeout:
                        continue
                    except socket.error as e:
                        logger.error(f"Socket 錯誤: {e}")
                        break

            except Exception as e:
                logger.error(f"SocketWorker 運行時發生異常: {e}")
            finally:
                self._safe_close_socket()

            # 如果是因為強制斷線，不要重連
            if self.force_disconnect:
                break

            # 如果是因為 paused 結束而斷線，等待重連
            if self.running and not self.paused and not self.force_disconnect:
                logger.info("準備重新連線...")
                time.sleep(1)  # 短暫延遲後重連

        # 確保最終清理
        self._safe_close_socket()
        data = {"status": False, "host": self.host}
        self.sig_socket_connect.emit(data)

    def pause(self):
        """暫停數據接收，但保持連線"""
        with QMutexLocker(self.connection_mutex):
            self.paused = True
            # 先清掉尚未湊滿一組的殘留
            self.temp_data = ""
            # 可選：立刻把 OS 緩衝也清一次
            self._drain_socket()
            logger.info("SocketWorker 已暫停")

    def resume(self):
        """恢復數據接收"""
        with QMutexLocker(self.connection_mutex):
            logger.info("恢復數據接收...")
            # 清理暫存資料
            self.temp_data = ""
            self.collect_data = ""
            # 設定為非暫停狀態
            self.paused = False
            # 如果沒有連線，會在 run() 迴圈中自動重連
            if not self.connected:
                logger.info("目前未連線，將在 run() 迴圈中重新連線")

    def _drain_socket(self):
        """非阻塞讀取並丟棄所有已到達的資料，直到目前無資料可讀"""
        if not self.sock:
            return
        try:
            # 暫時改成非阻塞
            self.sock.setblocking(False)
            while True:
                try:
                    # 單次讀大一些；讀到 0 代表對端關閉
                    chunk = self.sock.recv(65536)
                    if not chunk:
                        break
                    # 直接丟棄，不做任何處理
                except (BlockingIOError, InterruptedError):
                    # 沒資料可讀了
                    break
        finally:
            # 復原阻塞/timeout 設定
            self.sock.setblocking(True)
            self.sock.settimeout(3)

    def stop(self):
        """安全停止 SocketWorker"""
        logger.info("正在停止 SocketWorker...")
        with QMutexLocker(self.connection_mutex):
            self.running = False
            self.force_disconnect = True
            self.paused = False

        self._safe_close_socket()
        self.quit()
        if not self.wait(1000):
            logger.warning("SocketWorker 未能正常結束，強制終止")
            self.terminate()
            self.wait()

    def close_socket(self):
        """向後相容的方法，呼叫新的安全關閉方法"""
        self._safe_close_socket()

    def _safe_close_socket(self):
        """安全關閉 socket 連線"""
        with QMutexLocker(self.connection_mutex):
            if self.sock:
                try:
                    logger.info(f"關閉與 {self.host}:{self.port} 的連線")
                    self.sock.shutdown(socket.SHUT_RDWR)
                except Exception as e:
                    logger.debug(f"關閉 socket 時發生異常: {e}")
                finally:
                    try:
                        self.sock.close()
                    except Exception as e:
                        logger.debug(f"關閉 socket 時發生異常: {e}")
                    self.sock = None
                    self.connected = False

    def emit_data(self, hex_data):
        if hex_data:
            self.raw_data_received.emit(hex_data)

    def is_connected(self):
        """檢查是否已連線"""
        with QMutexLocker(self.connection_mutex):
            return self.connected and self.sock is not None

    def get_connection_info(self):
        """取得連線資訊"""
        with QMutexLocker(self.connection_mutex):
            return {
                "host": self.host,
                "port": self.port,
                "connected": self.connected,
                "running": self.running,
                "paused": self.paused,
                "force_disconnect": self.force_disconnect
            }

    def receive_chunk(self, sample_N, sample_byte):
        count_get = 0
        collect_data = ""
        self.temp_data = ""  # ← 如需保留殘留就不要在這裡清；視你的協議而定

        try:
            if not self.running or not self.sock:
                raise Exception("Client is not running or socket is invalid.")

            while count_get < sample_N:
                data_recv = self.sock.recv(sample_byte).hex()
                # logger.debug(f"收到資料：{data_recv}")
                if not data_recv:
                    raise Exception("Connection closed by the server.")
                collect_data += data_recv

                if self.temp_data:
                    data_recv = self.temp_data + data_recv

                N = len(data_recv) // 32
                count_get += N

                if len(data_recv) % 32 > 0:
                    self.temp_data = data_recv[N*32:]
                else:
                    self.temp_data = ""   # ✅ 修正：不要設成 []

                remaining_samples = sample_N - count_get
                sample_byte = max((remaining_samples * 32 - len(self.temp_data)) // 2, 1)

            return collect_data

        except socket.error as e:
            logger.error(f"Socket 錯誤: {e}")
            self.connected = False
            raise

    def _connect_socket(self):
        """建立 socket 連線"""
        with QMutexLocker(self.connection_mutex):
            # 如果已經連線，先關閉舊連線
            if self.connected and self.sock:
                logger.info("檢測到現有連線，先關閉舊連線")
                self._safe_close_socket_internal()

            try:
                logger.info(f"正在連接到 {self.host}:{self.port}...")
                self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                self.sock.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
                self.sock.settimeout(5)  # 連線超時設為 5 秒
                self.sock.connect((self.host, self.port))
                self.sock.settimeout(3)  # 數據接收超時設為 3 秒
                self.connected = True
                logger.info(f"✅ 成功連接到 {self.host}:{self.port}")

                # 發送連線成功信號
                data = {"status": True, "host": self.host}
                self.sig_socket_connect.emit(data)

            except Exception as e:
                self.connected = False
                if self.sock:
                    try:
                        self.sock.close()
                    except:
                        pass
                    self.sock = None
                logger.error(f"❌ 連接失敗 {self.host}:{self.port} - {e}")
                raise

    def _safe_close_socket_internal(self):
        """內部使用的安全關閉方法（不使用互斥鎖）"""
        if self.sock:
            try:
                self.sock.shutdown(socket.SHUT_RDWR)
            except Exception:
                pass
            finally:
                try:
                    self.sock.close()
                except Exception:
                    pass
                self.sock = None
                self.connected = False
