#!/usr/bin/env python3
"""
連線穩定性測試腳本
測試修復後的 SocketWorker 在裝置切換時的穩定性
"""

import sys
import time
import threading
from PySide2.QtCore import QCoreApplication, QTimer
from PySide2.QtWidgets import QApplication

# 添加專案路徑
sys.path.append('.')

from utils.socket_work import SocketWorker
from utils import logger

class ConnectionTest:
    def __init__(self):
        self.workers = []
        self.test_devices = [
            {"host": "*************", "port": 1333, "name": "Device1"},
            {"host": "*************", "port": 1333, "name": "Device2"},
            {"host": "*************", "port": 1333, "name": "Device3"},
        ]
        self.current_worker = None
        self.test_count = 0
        self.max_tests = 10
        
    def test_single_connection(self, device_info):
        """測試單一裝置連線"""
        logger.info(f"測試連線到 {device_info['name']} ({device_info['host']}:{device_info['port']})")
        
        worker = SocketWorker(host=device_info['host'], port=device_info['port'])
        worker.raw_data_received.connect(self.on_data_received)
        worker.sig_socket_connect.connect(self.on_connection_status)
        
        # 啟動 worker
        worker.start()
        
        # 等待連線建立
        time.sleep(2)
        
        # 檢查連線狀態
        if hasattr(worker, 'is_connected') and worker.is_connected():
            logger.info(f"✅ {device_info['name']} 連線成功")
            
            # 暫停和恢復測試
            logger.info(f"測試 {device_info['name']} 暫停/恢復功能")
            worker.pause()
            time.sleep(1)
            worker.resume()
            time.sleep(1)
            
        else:
            logger.warning(f"❌ {device_info['name']} 連線失敗")
        
        # 停止 worker
        worker.stop()
        time.sleep(1)
        
        return worker
    
    def test_device_switching(self):
        """測試裝置切換"""
        logger.info("開始測試裝置切換...")
        
        for i in range(self.max_tests):
            self.test_count = i + 1
            logger.info(f"\n=== 測試輪次 {self.test_count}/{self.max_tests} ===")
            
            for device_info in self.test_devices:
                # 如果有現有的 worker，先停止
                if self.current_worker:
                    logger.info("停止現有連線...")
                    self.current_worker.stop()
                    time.sleep(1)
                
                # 建立新連線
                self.current_worker = self.test_single_connection(device_info)
                
                # 短暫等待
                time.sleep(2)
            
            logger.info(f"完成第 {self.test_count} 輪測試")
        
        logger.info("所有測試完成！")
    
    def test_concurrent_connections(self):
        """測試同時多個連線（應該要能正確處理衝突）"""
        logger.info("測試同時多個連線...")
        
        workers = []
        for device_info in self.test_devices:
            worker = SocketWorker(host=device_info['host'], port=device_info['port'])
            worker.raw_data_received.connect(self.on_data_received)
            worker.sig_socket_connect.connect(self.on_connection_status)
            workers.append(worker)
        
        # 同時啟動所有 workers
        for worker in workers:
            worker.start()
        
        # 等待一段時間
        time.sleep(5)
        
        # 停止所有 workers
        for worker in workers:
            worker.stop()
        
        time.sleep(2)
        logger.info("同時連線測試完成")
    
    def on_data_received(self, data):
        """處理接收到的數據"""
        # 只記錄數據長度，避免日誌過多
        if len(data) > 0:
            logger.debug(f"收到數據，長度: {len(data)}")
    
    def on_connection_status(self, status_data):
        """處理連線狀態變化"""
        host = status_data.get('host', 'Unknown')
        status = status_data.get('status', False)
        status_text = "連線" if status else "斷線"
        logger.info(f"連線狀態變化: {host} - {status_text}")
    
    def run_all_tests(self):
        """執行所有測試"""
        logger.info("開始連線穩定性測試...")
        
        try:
            # 測試 1: 裝置切換
            self.test_device_switching()
            
            # 測試 2: 同時連線
            self.test_concurrent_connections()
            
            logger.info("✅ 所有測試完成！")
            
        except Exception as e:
            logger.error(f"❌ 測試過程中發生錯誤: {e}")
        
        finally:
            # 清理
            if self.current_worker:
                self.current_worker.stop()

def main():
    """主函數"""
    app = QCoreApplication(sys.argv)
    
    # 創建測試實例
    test = ConnectionTest()
    
    # 使用 QTimer 來執行測試，避免阻塞事件循環
    QTimer.singleShot(1000, test.run_all_tests)
    QTimer.singleShot(60000, app.quit)  # 60 秒後自動退出
    
    # 執行應用程式
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
