from utils.database import Database

class DatabaseModel:
    def __init__(self):
        self.db = Database('./db/machradar.mcdb')
        self.current_item = None

    def add_item(self, table_name, **kwargs):
        return self.db.create(table_name, **kwargs)

    def get_items(self, table_name, item_id=None):
        return self.db.read(table_name, item_id)

    def get_item(self,table_name, item_id):
        return self.db.read(table_name, item_id)

    def update_item(self, table_name, item_id, **kwargs):
        self.db.update(table_name, item_id, **kwargs)

    def delete_item(self, table_name, item_id):
        self.db.delete(table_name, item_id)

    def deleteTable(self, table_name):
        self.db.deleteTable(table_name)
    
    def getTableCount(self, table_name):
        return self.db.count(table_name)

    def get_countTableIP(self,table_name, ip):
        return self.db.countTableIP(table_name, ip)
    
    def get_copyFilteredData(self,source_table, destination_table, condition):
        return self.db.copyFilteredData(source_table, destination_table, condition)

    def get_copyFilteredDataByToolip(self,source_table, destination_table, toolip_value):
        return self.db.copy_filtered_data_by_toolip(source_table, destination_table, toolip_value)
    
    def get_query(self, table_name, conditions=None, order_by=None, limit=None):
        return self.db.query(table_name, conditions, order_by, limit)

    def post_update(self, table_name, update_values, conditions):
        """
        更新資料庫內的指定表格資料。

        參數:
        - table_name (str): 資料表名稱，例如 'tool_setting'。
        - update_values (dict): 要更新的欄位和值，例如：
            {'bending_x': 1.23, 'bending_y': 2.34, 'tension': 3.45, 'torsion': 4.56}  
        - conditions (dict): 選擇要更新的條件，例如：
            {'toolip': '*************'}

        回傳:
        - 執行更新的結果，取決於 `self.db.update()` 的回傳值。

        使用範例:
        ```python
        resultsUpdate = machDatabase.post_update(
            'tool_setting',
            update_values={'bending_x': Tare_BX, 'bending_y': Tare_BY, 'tension': Tare_Tension, 'torsion': Tare_Torsion},
            conditions={'toolip': ToolHolderIP}
        )
        ```
        """
        return self.db.update(table_name, update_values, conditions)

    def copy_table(self, table_to_copy_from, table_to_copy_to):
        self.db.copy_table(table_to_copy_from, table_to_copy_to)

    def compare_tables(self, table1_name, table2_name):
        """比較兩個資料表的資料是否相同"""
        return self.db.compare_tables(table1_name, table2_name)

    def close_db(self):
        self.db.close()

    def open_db(self):
        # 重新開啟資料庫連線
        self.db = Database('./db/machradar.mcdb')
    
    def insert_CO2_data(self,co2_data):
        return self.db.insert_CO2_data(co2_data)
    
    def insert_CO2_data_temp(self,co2_data):
        return self.db.insert_CO2_data_temp(co2_data)