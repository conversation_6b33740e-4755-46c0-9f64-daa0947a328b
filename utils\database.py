import sqlite3
from datetime import datetime
from . import logger
class Database:
    def __init__(self, db_name):
        self.conn = sqlite3.connect(db_name)
        self.conn.row_factory = sqlite3.Row  # 加了這段才能用欄位名稱查詢
        self.cursor = self.conn.cursor()
        self.create_machradar_setting_table() # 這是軟體設定資料表
        self.create_tool_magazine_table() # 這是刀庫資料表
        self.create_tool_setting_table() # 這是刀庫temp資料表
        self.create_CO2_table()

    # # 動態新增資料表 尚未使用
    # def create_table(self, table_name, columns):
    #     columns_str = ", ".join([f"{column_name} {column_type}" for column_name, column_type in columns.items()])
    #     self.cursor.execute(f'CREATE TABLE IF NOT EXISTS {table_name} ({columns_str})')
    #     self.conn.commit()

    def create_machradar_setting_table(self):
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS machradar_setting (
            id INTEGER PRIMARY KEY,
            communication TEXT DEFAULT "#BMW",
            msra_file_path TEXT NOT NULL,
            language TEXT DEFAULT 0,
            ip_start INTEGER DEFAULT 112, 
            ip_end INTEGER DEFAULT 117, 
            display_decimal_places INTEGER DEFAULT 3,
            record_decimal_places INTEGER DEFAULT 6,
            server NUMERIC DEFAULT False,
            localtime DATETIME DEFAULT (datetime('now', 'localtime'))

        )
        ''')
        self.conn.commit()

    # 這是刀酷資料表
    def create_tool_magazine_table(self):
        sql = ('''
        CREATE TABLE IF NOT EXISTS {table_name} (
            id INTEGER PRIMARY KEY,
            toolname TEXT NOT NULL,
            toolip TEXT NOT NULL,
            toolmac TEXT NOT NULL,
            sample_rate INTEGER NOT NULL, 
            tare_xv REAL NOT NULL, 
            tare_yv REAL NOT NULL, 
            tare_zv REAL NOT NULL,
            tare_tv REAL NOT NULL,
            Linear_x REAL NOT NULL,
            Linear_y REAL NOT NULL,
            Linear_z REAL NOT NULL,
            Linear_t REAL NOT NULL,
            tare_gx REAL DEFAULT 0,
            tare_gy REAL DEFAULT 0,
            tare_gz REAL DEFAULT 0,
            auto_record_enabled INTEGER DEFAULT 0, 
            auto_pre_record_seconds INTEGER DEFAULT 0,
            auto_record_seconds INTEGER DEFAULT 10,
            auto_max_record_count INTEGER DEFAULT 100,
            auto_cf_enabled INTEGER DEFAULT 0,
            auto_fz_enabled INTEGER DEFAULT 0,
            auto_t_enabled INTEGER DEFAULT 0,
            auto_cf_threshold REAL DEFAULT 0,
            auto_fz_threshold REAL DEFAULT 0,
            auto_t_threshold REAL DEFAULT 0,
            Lc REAL DEFAULT 0,
            Hl REAL DEFAULT 0,
            Kl REAL DEFAULT 0,
            Bx_COMP REAL DEFAULT 0,
            By_COMP REAL DEFAULT 0,
            Bz_COMP REAL DEFAULT 0,
            Bt_COMP REAL DEFAULT 0,
            localtime DATETIME DEFAULT (datetime('now', 'localtime')),
            CO2_id INTEGER DEFAULT NULL,
            FOREIGN KEY (CO2_id) REFERENCES machradar_co2(id)
        )
        ''')
        self.cursor.execute(sql.format(table_name='tool_magazine'))
        self.cursor.execute(sql.format(table_name='tool_magazine_temp'))
        self.conn.commit()

    def create_tool_setting_table(self):
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS tool_setting (
            id INTEGER PRIMARY KEY,
            toolname TEXT NOT NULL,
            toolip TEXT NOT NULL,
            toolmac TEXT NOT NULL,
            sample_rate INTEGER NOT NULL, 
            tare_xv REAL NOT NULL, 
            tare_yv REAL NOT NULL, 
            tare_zv REAL NOT NULL,
            tare_tv REAL NOT NULL,
            Linear_x REAL NOT NULL,
            Linear_y REAL NOT NULL,
            Linear_z REAL NOT NULL,
            Linear_t REAL NOT NULL,
            tare_gx REAL DEFAULT 0,
            tare_gy REAL DEFAULT 0,
            tare_gz REAL DEFAULT 0,
            auto_record_enabled INTEGER DEFAULT 0, 
            auto_pre_record_seconds INTEGER DEFAULT 0,
            auto_record_seconds INTEGER DEFAULT 10,
            auto_max_record_count INTEGER DEFAULT 100,
            auto_cf_enabled INTEGER DEFAULT 0,
            auto_fz_enabled INTEGER DEFAULT 0,
            auto_t_enabled INTEGER DEFAULT 0,
            auto_cf_threshold REAL DEFAULT 0,
            auto_fz_threshold REAL DEFAULT 0,
            auto_t_threshold REAL DEFAULT 0,
            Lc REAL DEFAULT 0,
            Hl REAL DEFAULT 0,
            Kl REAL DEFAULT 0,
            Bx_COMP REAL DEFAULT 0,
            By_COMP REAL DEFAULT 0,
            Bz_COMP REAL DEFAULT 0,
            Bt_COMP REAL DEFAULT 0,
            localtime DATETIME DEFAULT (datetime('now', 'localtime')),
            CO2_id INTEGER DEFAULT NULL,
            FOREIGN KEY (CO2_id) REFERENCES machradar_co2(id)
        )
        ''')
        self.conn.commit()

    def create_CO2_table(self):
        """
        init_status = 0: 未初始化
        init_status = 1: 已初始化
        """
        sql = ('''
        CREATE TABLE IF NOT EXISTS {table_name} (
            id INTEGER PRIMARY KEY,
            MS_CO2_K INTEGER DEFAULT 0 NOT NULL,
            MS_CO2_zc INTEGER DEFAULT 0 NOT NULL,
            MS_CO2_Dc REAL DEFAULT 0 NOT NULL, 
            MS_CO2_vc REAL DEFAULT 0 NOT NULL, 
            MS_CO2_fz REAL DEFAULT 0 NOT NULL, 
            MS_CO2_ap REAL DEFAULT 0 NOT NULL,
            MS_CO2_ae REAL DEFAULT 0 NOT NULL,
            MS_CO2_n REAL DEFAULT 0 NOT NULL,
            MS_CO2_vf REAL DEFAULT 0 NOT NULL,
            MS_CO2_Q REAL DEFAULT 0 NOT NULL,
            MS_CO2_Pc REAL DEFAULT 0 NOT NULL,
            MS_CO2_Pb REAL DEFAULT 0 NOT NULL, 
            MS_CO2_EF REAL DEFAULT 0 NOT NULL,
            init_status INTEGER DEFAULT 0 NOT NULL,
            localtime DATETIME DEFAULT (datetime('now', 'localtime'))
        )
        ''')
        self.cursor.execute(sql.format(table_name='machradar_co2'))
        self.cursor.execute(sql.format(table_name='machradar_co2_temp'))
        self.conn.commit()

    def create(self, table_name, **kwargs):
        columns = ", ".join(kwargs.keys())
        placeholders = ", ".join(["?"] * len(kwargs))
        values = list(kwargs.values())
        self.cursor.execute(f'INSERT INTO {table_name} ({columns}) VALUES ({placeholders})', values)
        self.conn.commit()
        return self.cursor.lastrowid

    def read(self, table_name, item_id=None):
        if item_id:
            self.cursor.execute(f'SELECT * FROM {table_name} WHERE id = ?', (item_id,))
            return self.cursor.fetchone()
        else:
            self.cursor.execute(f'SELECT * FROM {table_name}')
            return self.cursor.fetchall()

    def update(self, table_name, item_id, **kwargs):
        set_clause = ", ".join([f"{key} = ?" for key in kwargs])
        values = list(kwargs.values()) + [item_id]
        self.cursor.execute(f'UPDATE {table_name} SET {set_clause} WHERE id = ?', values)
        self.conn.commit()

    def delete(self, table_name, item_id):
        self.cursor.execute(f'DELETE FROM {table_name} WHERE id = ?', (item_id,))
        self.conn.commit()

    def deleteTable(self, table_name):
        self.cursor.execute(f'DELETE FROM {table_name}')
        self.conn.commit()

    # 查看資料筆數
    def count(self, table_name):
        self.cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = self.cursor.fetchone()[0]  # 取得 COUNT(*) 的值
        return count

    def countTableIP(self, table_name ,ip):
        self.cursor.execute(f'SELECT COUNT(*) FROM {table_name} WHERE toolip = ?', (ip,))
        # count = self.cursor.fetchone()[0]
        return self.cursor.fetchone()[0]
    
    def copyFilteredData(self,source_table, destination_table, condition):
        # 構建 SQL 查詢
        sql = f"""
        INSERT INTO {destination_table} (column1, column2, column3, ...)
        SELECT column1, column2, column3, ...
        FROM {source_table}
        WHERE {condition};
        """

        # 執行 SQL 查詢
        self.cursor.execute(sql)
        
        # 提交變更
        self.conn.commit()

    def copy_filtered_data_by_toolip(self,source_table, destination_table, toolip_value):
        
        # 構建 SQL 查詢
        sql = f"""
        INSERT INTO {destination_table} (toolname, toolip, sample_rate, bending_x, bending_y, tension, torsion, unit_x, unit_y, unit_ten, unit_tor, auto_record_enabled, auto_cf_threshold, auto_fz_threshold, auto_t_threshold, auto_pre_record_seconds, auto_record_seconds, auto_max_record_count, auto_cf_enabled, auto_fz_enabled, auto_t_enabled, CO2_id)
        SELECT toolname, toolip, sample_rate, bending_x, bending_y, tension, torsion, unit_x, unit_y, unit_ten, unit_tor, auto_record_enabled, auto_cf_threshold, auto_fz_threshold, auto_t_threshold, auto_pre_record_seconds, auto_record_seconds, auto_max_record_count, auto_cf_enabled, auto_fz_enabled, auto_t_enabled, CO2_id
        FROM {source_table}
        WHERE toolip = ?;
        """
        
        # 執行 SQL 查詢
        self.cursor.execute(sql, (toolip_value,))
        
        # self.cursor.execute(f'DELETE FROM {table_name} WHERE id = ?', (item_id,))
        # self.conn.commit()
        # 提交變更
        self.conn.commit()

    def insert_CO2_data(self, co2_data):
        # 插入 CO2 數據
        sql = '''
        INSERT INTO machradar_co2 (MS_CO2_K, MS_CO2_zc, MS_CO2_Dc, MS_CO2_vc, MS_CO2_fz, MS_CO2_ap,MS_CO2_ae, MS_CO2_n, MS_CO2_vf, MS_CO2_Q, MS_CO2_Pc, MS_CO2_Pb, MS_CO2_EF)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        self.cursor.execute(sql, (
            co2_data["MS_CO2_K"], co2_data["MS_CO2_zc"], co2_data["MS_CO2_Dc"], co2_data["MS_CO2_vc"],
            co2_data["MS_CO2_fz"], co2_data["MS_CO2_ap"], co2_data["MS_CO2_ae"], co2_data["MS_CO2_n"],
            co2_data["MS_CO2_vf"], co2_data["MS_CO2_Q"], co2_data["MS_CO2_Pc"], co2_data["MS_CO2_Pb"],
            co2_data["MS_CO2_EF"]
        ))
        co2_id = self.cursor.lastrowid
        self.conn.commit()
        return co2_id

    def insert_CO2_data_temp(self, co2_data):
        # 插入 CO2 數據到暫存表格
        sql = '''
        INSERT INTO machradar_co2_temp (MS_CO2_K, MS_CO2_zc, MS_CO2_Dc, MS_CO2_vc, MS_CO2_fz, MS_CO2_ap,MS_CO2_ae, MS_CO2_n, MS_CO2_vf, MS_CO2_Q, MS_CO2_Pc, MS_CO2_Pb, MS_CO2_EF)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        '''
        self.cursor.execute(sql, (
            co2_data["MS_CO2_K"], co2_data["MS_CO2_zc"], co2_data["MS_CO2_Dc"], co2_data["MS_CO2_vc"],
            co2_data["MS_CO2_fz"], co2_data["MS_CO2_ap"], co2_data["MS_CO2_ae"], co2_data["MS_CO2_n"],
            co2_data["MS_CO2_vf"], co2_data["MS_CO2_Q"], co2_data["MS_CO2_Pc"], co2_data["MS_CO2_Pb"],
            co2_data["MS_CO2_EF"]
        ))
        co2_id = self.cursor.lastrowid
        self.conn.commit()
        return co2_id

    # 查詢 
    # # 查詢名字為 Alice 或 Bob 的用戶
    # results = db.query('users', conditions={'name': ['Alice', 'Bob']})
    def query(self, table_name, conditions=None, order_by=None, limit=None):
        sql = f'SELECT * FROM "{table_name}"'
        values = []

        if conditions:
            where_clause = []
            for key, value in conditions.items():
                if isinstance(value, (list, tuple)):
                    where_clause.append(f"{key} IN ({','.join(['?']*len(value))})")
                    values.extend(value)
                else:
                    where_clause.append(f'"{key}" = ?')
                    values.append(value)
            sql += " WHERE " + " AND ".join(where_clause)

        if order_by:
            sql += f" ORDER BY {order_by}"

        if limit:
            sql += f" LIMIT {limit}"

        self.cursor.execute(sql, values)
        return self.cursor.fetchall()

    # 修改
    # 更新 users 表中 name 為 'Alice' 的記錄，將 age 設置為 31
    # updated_rows = db.update('users',update_values={'age': 31},conditions={'name': 'Alice'})
    def update(self, table_name, update_values, conditions):
        """
        更新表中的記錄。
        :param table_name: 要更新的表名
        :param update_values: 要更新的欄位和值的字典
        :param conditions: 更新條件的字典
        """

        # 添加當前時間戳
        update_values['localtime'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        # 更新 Tool 關聯 CO2_id
        set_clause = ", ".join([f"{key} = ?" for key in update_values])
        where_clause = " AND ".join([f"{key} = ?" for key in conditions])
        
        sql = f"UPDATE {table_name} SET {set_clause} WHERE {where_clause}"
        values = list(update_values.values()) + list(conditions.values())
        
        self.cursor.execute(sql, values)
        self.conn.commit()
        return self.cursor.rowcount  # 返回更新的行數

    def copy_table(self, table_to_copy_from, table_to_copy_to):
        self.deleteTable(table_to_copy_to)
        
        sql = (f"""
        INSERT INTO {table_to_copy_to}
        SELECT * FROM {table_to_copy_from};
        """)
        self.cursor.execute(sql)
        self.conn.commit()

    def compare_tables(self, table1_name, table2_name):
        try:
            # 從 table1 獲取所有資料
            table1_data = self.read(table1_name)
            
            # 從 table2 獲取所有資料
            table2_data = self.read(table2_name)
            # 比較資料
            return table1_data == table2_data

        except Exception as e:
            logger.error(f"比較 {table1_name} 和 {table2_name} 的資料時發生錯誤：{e} | 返回 True")
            return True

    def close(self):
        self.conn.close()

    # machDatabase.add_item('tool_setting', toolname='Serena_TEST', toolip='*************',sample_rate = 10000,bending_x = 5.428, bending_y = 0.014,tension = 0.037,torsion = 0.071,unit_x = 30.0,unit_y = 30.0,unit_ten = 40.0,unit_tor = 40.0)

    # machDatabase.add_item('tool_setting', toolname='Serena_XXX', toolip='*************',sample_rate = 10000,bending_x = 5.428, bending_y = 0.014,tension = 0.037,torsion = 0.071,unit_x = 30.0,unit_y = 30.0,unit_ten = 40.0,unit_tor = 40.0)

    # machDatabase.add_item('tool_magazine', toolname='Serena_TEST', toolip='*************',sample_rate = 10000,bending_x = 5.428, bending_y = 0.014,tension = 0.037,torsion = 0.071,unit_x = 30.0,unit_y = 30.0,unit_ten = 40.0,unit_tor = 40.0)

    # machDatabase.add_item('tool_magazine', toolname='Serena_XXX', toolip='*************',sample_rate = 10000,bending_x = 5.428, bending_y = 0.014,tension = 0.037,torsion = 0.071,unit_x = 30.0,unit_y = 30.0,unit_ten = 40.0,unit_tor = 40.0)