# main.py
import sys
from PySide2.QtWidgets import QApplication
from PySide2.QtCore import QTimer
from app.models.model import Model
from app.views.view import View
from app.controllers.controller import Controller
from app.models.database_model import DatabaseModel
from app.dialogs.loading_window import Ui_Loading_Window

def main():
    app = QApplication(sys.argv)

    # 顯示 loading 視窗
    loading_window = Ui_Loading_Window()
    loading_window.center_on_screen()
    loading_window.show()
    
    # 處理事件讓 loading 視窗顯示
    app.processEvents()

    # 資料庫建置與確認
    loading_window.update_progress(20, "正在初始化資料庫...")
    app.processEvents()
    
    machDatabase = DatabaseModel()

    # 軟體設定
    loading_window.update_progress(40, "正在載入設定...")
    app.processEvents()
    
    setting_items = machDatabase.getTableCount('machradar_setting')
    if setting_items < 1:
        machDatabase.add_item('machradar_setting',  communication='#BMW',  msra_file_path=r"D:\Record", language = '0', ip_start = 112,  ip_end = 117, server = 0)

    machDatabase.close_db()

    # 初始化模型、視圖和控制器
    loading_window.update_progress(60, "正在初始化模型...")
    app.processEvents()
    
    model = Model()
    model.start_up()
    
    loading_window.update_progress(80, "正在載入介面...")
    app.processEvents()
    
    view = View()
    controller = Controller(model, view)

    loading_window.update_progress(100, "載入完成")
    app.processEvents()
    
    # 延遲一下讓使用者看到完成狀態
    QTimer.singleShot(500, lambda: [loading_window.close(), view.show()])

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
