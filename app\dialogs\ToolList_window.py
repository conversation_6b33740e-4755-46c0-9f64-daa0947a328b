# -*- coding: utf-8 -*-

from PySide2.QtCore import *
from PySide2.QtGui import *
from PySide2.QtWidgets import *
import system_image.system_image_rc
from dataclasses import dataclass
from typing import Dict, Any
from .. import logger 

@dataclass
class ToolItemData:
    """工具項目的數據結構"""
    id: str                  # 工具ID
    name: str               # 工具名稱
    index: int              # UI中的索引
    settings: Dict[str, Any] # 工具設置
    connection_status: bool  # 連接狀態

class Ui_ToolList_Window(object):
    def __init__(self):
        super().__init__()
        self.tool_items = []  # 存儲UI元素
        self.tool_data = {}   # 存儲工具數據 {id: ToolItemData}

    def setupUi(self, ToolList_Window):
        if not ToolList_Window.objectName():
            ToolList_Window.setObjectName(u"ToolList_Window")
        ToolList_Window.resize(350, 270)
        sizePolicy = QSizePolicy(QSizePolicy.MinimumExpanding, QSizePolicy.MinimumExpanding)
        sizePolicy.setHorizontalStretch(0)
        sizePolicy.setVerticalStretch(0)
        sizePolicy.setHeightForWidth(ToolList_Window.sizePolicy().hasHeightForWidth())
        ToolList_Window.setSizePolicy(sizePolicy)
        ToolList_Window.setMaximumSize(QSize(350, 270))
        ToolList_Window.setStyleSheet(u"QWidget #ToolList_Window{\n"
"    background-color: #0C0C44;\n"
"    border: 2px solid #5448B6;\n"
"}")

        # Set up the base layout and scroll area
        self.setupBaseLayout(ToolList_Window)
        
        # Initialize empty list to store tool items
        self.tool_items = []
        
        # Create the main widget that will contain tool items
        self.widget = QWidget(self.scrollAreaWidgetContents)
        self.widget.setObjectName(u"widget")
        sizePolicy = QSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        sizePolicy.setHeightForWidth(self.widget.sizePolicy().hasHeightForWidth())
        self.widget.setSizePolicy(sizePolicy)
        self.widget.setFont(QFont("Arial Black"))
        
        # Create vertical layout for tool items
        self.verticalLayout = QVBoxLayout(self.widget)
        self.verticalLayout.setSpacing(10)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setSizeConstraint(QLayout.SetDefaultConstraint)
        self.verticalLayout.setContentsMargins(2, 5, 2, 1)
        
        # Add spacer at the bottom
        self.verticalSpacer = QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding)
        self.verticalLayout.addItem(self.verticalSpacer)
        
        self.verticalLayout_2.addWidget(self.widget)
        
        QMetaObject.connectSlotsByName(ToolList_Window)

    def setupBaseLayout(self, ToolList_Window):
        """Set up the base layout and scroll area"""
        self.gridLayout = QVBoxLayout(ToolList_Window)  # 改為 QVBoxLayout 以便垂直排列
        self.gridLayout.setObjectName(u"gridLayout")
        self.gridLayout.setContentsMargins(5, 5, 5, 5)

        # Create and setup scroll area
        self.scrollArea = QScrollArea(ToolList_Window)
        self.scrollArea.setObjectName(u"scrollArea")
        self.scrollArea.setMinimumSize(QSize(340, 20))
        self.scrollArea.setFont(QFont("Arial Black"))
        self.scrollArea.setStyleSheet("QScrollArea { border: none;}")
        self.setupScrollBars()

        # Create scroll area contents
        self.scrollAreaWidgetContents = QWidget()
        self.scrollAreaWidgetContents.setObjectName(u"scrollAreaWidgetContents")
        self.scrollAreaWidgetContents.setGeometry(QRect(0, 0, 346, 266))
        self.scrollAreaWidgetContents.setStyleSheet(u"QWidget#scrollAreaWidgetContents {\n"
                                                    "background-color: #0C0C44;\n"
                                                    "border:none;\n"
                                                    "}")

        self.verticalLayout_2 = QVBoxLayout(self.scrollAreaWidgetContents)
        self.verticalLayout_2.setObjectName(u"verticalLayout_2")
        self.verticalLayout_2.setContentsMargins(1, 1, 1, 1)

        self.scrollArea.setWidget(self.scrollAreaWidgetContents)
        self.scrollArea.setWidgetResizable(True)
        self.gridLayout.addWidget(self.scrollArea)  # 先加入 scrollArea

        # === 新增 Button ===
        self.add_tool_btn = QPushButton("新增工具", ToolList_Window)
        self.add_tool_btn.setObjectName("add_tool_btn")
        self.add_tool_btn.setFont(QFont("Arial Black", 10))
        self.add_tool_btn.setStyleSheet("""
            QPushButton {
            color: rgb(255, 255, 255);
            background-color: #0C0C44;
            border: 2px solid #5448B6;
        	border-radius:5px;
        	padding:2px;
        }

        QPushButton:hover {
            background-color: #0C0C44;
        	border: 2px solid #7AFEC6;
            color: rgb(255, 255, 255);
        	border-radius:5px;
        	padding:2px;
        }

        QPushButton:pressed {
            background-color: gray;
        	border: none;
        }
                """)
        self.gridLayout.addWidget(self.add_tool_btn)  # 將按鈕加入 Layout，會顯示在 QScrollArea 下面


    def setupScrollBars(self):
        """Set up the scroll bar styles"""
        self.scrollArea.verticalScrollBar().setStyleSheet("""
            QScrollBar:vertical {
                background: #0C0C44;
                width: 8px;
                border: none;
                margin: 0px 0px 0px 0px;
            }
            QScrollBar::handle:vertical {
                background: #7AFEC6;
                min-height: 20px;
                border-radius: 3px;
            }
            QScrollBar::add-line:vertical, 
            QScrollBar::sub-line:vertical {
                background: none;
                height: 0px;
            }
            QScrollBar::sub-page:vertical, 
            QScrollBar::add-page:vertical {
                background: #0C0C44;
            }
        """)
        
        self.scrollArea.horizontalScrollBar().setStyleSheet("""
            QScrollBar:horizontal {
                background: #0C0C44;
                height: 2px;
                border: none;
                margin: 0px 0px 0px 0px;
            }
            QScrollBar::handle:horizontal {
                background: #7AFEC6;
                min-width: 20px;
                border-radius: 3px;
            }
            QScrollBar::add-line:horizontal, 
            QScrollBar::sub-line:horizontal {
                background: none;
                width: 0px;
            }
            QScrollBar::sub-page:horizontal, 
            QScrollBar::add-page:horizontal {
                background: #0C0C44;
            }
        """)

    def createToolItem(self, index: int, tool_name: str, tool_id: str) -> QWidget:
        """Create a new tool item widget"""
        tool_item = QWidget(self.widget)
        tool_item.setObjectName(f"Toolitem_window_{tool_id}")
        # tool_item.setMaximumSize(QSize(340, 30))
        tool_item.setFont(QFont("Arial Black"))
        
        # Create horizontal layout
        h_layout = QHBoxLayout(tool_item)
        h_layout.setSpacing(5)
        h_layout.setObjectName(f"horizontalLayout_{tool_id}")
        h_layout.setSizeConstraint(QLayout.SetDefaultConstraint)
        h_layout.setContentsMargins(1, 0, 1, 0)
        
        # Create and add label
        label = QLabel(tool_item)
        label.setObjectName(f"toolname_label_{tool_id}")
        label.setFont(QFont("Arial Black"))
        label.setStyleSheet("color: rgb(255, 255, 255);")
        label.setText(tool_name)
        h_layout.addWidget(label)
        
        # Create and add link icon button
        link_icon = QPushButton(tool_item)
        link_icon.setObjectName(f"Tool_link_icon_{tool_id}")
        link_icon.setStyleSheet("border:none;")
        link_icon.setCheckable(True)
        icon = QIcon()
        icon.addFile(":/link_ico/offline.png", QSize(), QIcon.Disabled, QIcon.Off)
        icon.addFile(":/link_ico/online.png", QSize(), QIcon.Disabled, QIcon.On)
        link_icon.setIcon(icon)
        h_layout.addWidget(link_icon)
        link_icon.setEnabled(False)

        # link_icon.toggled.connect(lambda checked: print(f"Tool {tool_id} link icon toggled: {checked}"))
        
        # Create and add setting button
        setting_btn = QPushButton(tool_item)
        setting_btn.setObjectName(f"Tool_setting_Btn_{tool_id}")
        setting_btn.setFont(QFont("Arial Black", 10))
        setting_btn.setText("設定")
        setting_btn.setCheckable(True)
        setting_btn.setStyleSheet("""
            QPushButton {
                color: rgb(255, 255, 255);
                background-color: #0C0C44;
                border: 2px solid #5448B6;
                border-radius: 5px;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: #0C0C44;
                border: 2px solid #7AFEC6;
                color: rgb(255, 255, 255);
            }
            QPushButton:pressed {
                background-color: gray;
                border: none;
            }
        """)
        h_layout.addWidget(setting_btn)
        
        # Create and add link button
        link_btn = QPushButton(tool_item)
        link_btn.setObjectName(f"Tool_link_Btn_{tool_id}")
        link_btn.setFont(QFont("Arial Black", 10))
        link_btn.setText("連接")
        link_btn.setCheckable(True)
        link_btn.setStyleSheet("""
            QPushButton {
                color: rgb(255, 255, 255);
                background-color: #5448B6;
                border: 2px solid #5448B6;
                border-radius: 5px;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: #7AFEC6;
                color: rgb(255, 255, 255);
                border: none;
            }
            QPushButton:pressed {
                background-color: gray;
                border: none;
            }
        """)
        h_layout.addWidget(link_btn)
        
        # Set stretch factors
        h_layout.setStretch(0, 5)  # label
        h_layout.setStretch(1, 2)  # link icon
        h_layout.setStretch(2, 2)  # setting button
        h_layout.setStretch(3, 2)  # link button
        
        # Store references to the UI elements as class attributes
        setattr(self, f"Toolitem_window_{tool_id}", tool_item)
        setattr(self, f"toolname_label_{tool_id}", label)
        setattr(self, f"Tool_link_icon_{tool_id}", link_icon)
        setattr(self, f"Tool_setting_Btn_{tool_id}", setting_btn)
        setattr(self, f"Tool_link_Btn_{tool_id}", link_btn)

        # 創建並存儲工具數據
        tool_data = ToolItemData(
            id=tool_id,
            name=tool_name,
            index=index,
            settings={},
            connection_status=False
        )
        self.tool_data[tool_id] = tool_data
        
        # Insert the tool item before the spacer
        self.verticalLayout.insertWidget(len(self.tool_items), tool_item)
        self.tool_items.append(tool_item)
        
        return tool_item

    def addToolItem(self, tool_name: str, tool_id: str):
        """Add a new tool item to the list"""
        index = len(self.tool_items) + 1
        return self.createToolItem(index, tool_name, tool_id)

    def removeToolItem(self, index):
        """Remove a tool item from the list"""
        if 0 <= index < len(self.tool_items):
            tool_item = self.tool_items.pop(index)
            
            # Remove the class attributes
            delattr(self, f"Toolitem_window_{index + 1}")
            delattr(self, f"toolname_label_{index + 1}")
            delattr(self, f"Tool_link_icon_{index + 1}")
            delattr(self, f"Tool_setting_Btn_{index + 1}")
            delattr(self, f"Tool_link_Btn_{index + 1}")
            
            self.verticalLayout.removeWidget(tool_item)
            tool_item.deleteLater()

    def retranslateUi(self, ToolList_Window):
        ToolList_Window.setWindowTitle(QCoreApplication.translate("ToolList_Window", u"Form", None))

    def getToolData(self, tool_id: str) -> ToolItemData:
        """獲取工具數據"""
        return self.tool_data.get(tool_id)

    def updateToolSettings(self, tool_id: str, settings: Dict[str, Any]):
        """更新工具設置"""
        if tool_id in self.tool_data:
            self.tool_data[tool_id].settings.update(settings)

    def updateConnectionStatus(self, tool_id: str, status: bool):
        """更新工具連接狀態"""
        if tool_id in self.tool_data:
            tool_data = self.tool_data[tool_id]
            tool_data.connection_status = status
            
            # 更新UI顯示
            try:
                link_icon = getattr(self, f"Tool_link_icon_{tool_id}")
                if link_icon:
                    link_icon.setChecked(status)
                    logger.info(f"更新工具 {tool_id} 的連接狀態為: {'在線' if status else '離線'}")
                else:
                    logger.error(f"找不到圖標: Tool_link_icon_{tool_id}")
            except AttributeError as e:
                logger.error(f"更新圖標時發生錯誤: {e}")
            except Exception as e:
                logger.error(f"更新連接狀態時發生未預期的錯誤: {e}")

