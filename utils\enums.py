from enum import Enum
from PySide2.QtGui import QColor



class StressSafetyStatus(Enum):
    NO_DATA = -1
    SAFE = 0
    WARNING = 1
    ALARM = 2

    @property
    def color(self) -> QColor:
        color_map = {
            StressSafetyStatus.NO_DATA: QColor(0, 0, 0, 0), # 透明
            StressSafetyStatus.SAFE:    QColor(0, 0, 0, 0), # 透明
            StressSafetyStatus.WARNING: QColor(255, 255, 0, 255), # 黃色  
            StressSafetyStatus.ALARM:   QColor(255, 0, 0, 255), # 紅色      
        }        
        return color_map.get(self, QColor(0, 0, 0, 255)) #預設黑色

    @property
    def ui_color(self) -> QColor:
        color_map = {
            StressSafetyStatus.NO_DATA: QColor(0, 0, 118, 115), # 藍色
            StressSafetyStatus.SAFE:    QColor(0, 0, 118, 115), # 藍色
            StressSafetyStatus.WARNING: QColor(255, 255, 0, 115), # 黃色  
            StressSafetyStatus.ALARM:   QColor(255, 0, 0, 115), # 紅色      
        }        
        return color_map.get(self, QColor(0, 0, 0, 115)) #預設黑色
