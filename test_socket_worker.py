#!/usr/bin/env python3
"""
SocketWorker 基本功能測試
"""

import sys
import time
from PySide2.QtCore import QCoreApplication

# 添加專案路徑
sys.path.append('.')

from utils.socket_work import SocketWorker
from utils import logger

def test_socket_worker_basic():
    """測試 SocketWorker 基本功能"""
    logger.info("開始測試 SocketWorker 基本功能...")
    
    # 測試裝置資訊
    test_host = "*************"
    test_port = 1333
    
    # 創建 SocketWorker
    worker = SocketWorker(host=test_host, port=test_port)
    
    # 設置信號處理
    def on_data_received(data):
        logger.info(f"收到數據，長度: {len(data)}")
    
    def on_connection_status(status_data):
        host = status_data.get('host', 'Unknown')
        status = status_data.get('status', False)
        status_text = "連線成功" if status else "連線斷開"
        logger.info(f"連線狀態: {host} - {status_text}")
    
    worker.raw_data_received.connect(on_data_received)
    worker.sig_socket_connect.connect(on_connection_status)
    
    try:
        # 測試 1: 基本連線
        logger.info("測試 1: 基本連線")
        worker.start()
        time.sleep(3)
        
        # 檢查連線狀態
        if hasattr(worker, 'is_connected'):
            is_connected = worker.is_connected()
            logger.info(f"連線狀態: {'已連線' if is_connected else '未連線'}")
            
            if hasattr(worker, 'get_connection_info'):
                info = worker.get_connection_info()
                logger.info(f"連線資訊: {info}")
        
        # 測試 2: 暫停和恢復
        logger.info("測試 2: 暫停和恢復")
        worker.pause()
        time.sleep(2)
        logger.info("已暫停，準備恢復...")
        worker.resume()
        time.sleep(2)
        
        # 測試 3: 停止
        logger.info("測試 3: 停止連線")
        worker.stop()
        time.sleep(2)
        
        logger.info("✅ 基本功能測試完成")
        
    except Exception as e:
        logger.error(f"❌ 測試過程中發生錯誤: {e}")
    
    finally:
        # 確保清理
        if worker.isRunning():
            worker.stop()

def test_multiple_workers():
    """測試多個 SocketWorker 實例"""
    logger.info("開始測試多個 SocketWorker 實例...")
    
    workers = []
    test_devices = [
        {"host": "*************", "port": 1333},
        {"host": "*************", "port": 1333},
    ]
    
    try:
        # 創建多個 workers
        for i, device in enumerate(test_devices):
            worker = SocketWorker(host=device['host'], port=device['port'])
            
            def make_handler(worker_id):
                def on_connection_status(status_data):
                    host = status_data.get('host', 'Unknown')
                    status = status_data.get('status', False)
                    status_text = "連線成功" if status else "連線斷開"
                    logger.info(f"Worker {worker_id} - {host}: {status_text}")
                return on_connection_status
            
            worker.sig_socket_connect.connect(make_handler(i))
            workers.append(worker)
        
        # 依序啟動
        for i, worker in enumerate(workers):
            logger.info(f"啟動 Worker {i}")
            worker.start()
            time.sleep(2)
        
        # 等待一段時間
        time.sleep(5)
        
        # 依序停止
        for i, worker in enumerate(workers):
            logger.info(f"停止 Worker {i}")
            worker.stop()
            time.sleep(1)
        
        logger.info("✅ 多個 SocketWorker 測試完成")
        
    except Exception as e:
        logger.error(f"❌ 多個 SocketWorker 測試失敗: {e}")
    
    finally:
        # 確保清理
        for worker in workers:
            if worker.isRunning():
                worker.stop()

def main():
    """主函數"""
    app = QCoreApplication(sys.argv)
    
    logger.info("=== SocketWorker 測試開始 ===")
    
    # 執行測試
    test_socket_worker_basic()
    time.sleep(2)
    test_multiple_workers()
    
    logger.info("=== 所有測試完成 ===")
    
    app.quit()

if __name__ == "__main__":
    main()
