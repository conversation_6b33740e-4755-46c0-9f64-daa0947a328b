from ctypes import *
from ctypes.wintypes import *
import xml.etree.ElementTree as ET  #Icon_Machradar_Enter
import time
import logging
logger = logging.getLogger(__name__)

libc = windll.LoadLibrary("hasp_windows_x64_105443.dll") #如果是64bit應用程式請載入"hasp_windows_x64_<batch code>"
sentinel_vcode = "yH263hKGr4vichkwaUHczem/KLQq5zXDM/uE2/Yni2vnuu2cQ0ybvm9Cetxhbp5yl0xVGUQvYbpAnYD6Q48U4OqeDQp/EBFdb5Zi1c7TNKvEwJmUWKcNgUeZxyVp80GojR1n1XRwfwUlyFoJ0PbnPplvx49tIcDVWcoPt9Xl2TkmV5r97jjdcU1VkD7hBI1oc3Oc9HXoPpOFapG5adzukT4B7QDZjaPhHfSt2wZhanfvT7mumlR2UkFWQwrMTPeynB+G7UcMqfaDBbL/6e3uAiPRaCU23maZOnRHHGMo1qQEpdEtXQBX/P8ZcMP1rSmL9KRr5ce8TlXaiv4votbvfhXWnqfyHCjaCSdFnA6uZ2PBj0noY//hI1C91BiEfgHGlduM169ZMakue2h7sDUso56HJ31dEtit853Un0Str5isLqrz9/igH3kvWCmu5OKgIMrHamTX5WmhjR51cSNvNG/qNYqQ7qOWt4lRaTvGZsU6FB9aMB//A04wqjlq0KSU2vHSTHFIzFG8eMgMbB3TVqFR6Cj3q1w9EL1MaCk1zUQ2v0Fb6isY+QFb8L2Q8PLJ42FtrWjeCHLRtB9p72XgC9f1Ew5NepipCQP0lIcw1FwneXoRiUO5KaHMTtEJruwAndqcyvjicTRKOb7NKENYh7Rfes1M9rZeJMXZx631LC2nUG/VCFiGi2SVjbpuRNresaUFJ+DCrIYf7G4lRfyOF/O8+fQLH6Qg+S0HkMoHLTtIunR+zNik6vb0xcrCULD4VjJyoeYqrfZtaIGY9Qd8WWBf5CXvlSAyYdNOyoaDY91ArnX+/DwFC4KAqQ1Lv4OEeZ7wadWbSLhPMINXptShU26OemCPJgInDV803RVkeCZriJw1yXg38BIrsm4Ffnurv0y8hljr7mH5bOd2fpM/Ggw6yKHwA9v5wcZ7lXfj4RYRqHImnI4Vv0jwFsSivjLKljCJFREOWc4zlh+oHeLfhQ=="
sentinel_handle = c_int(0)
fid = 12 

def get_mvc_version():
    return mvc_version

def set_mvc_version(mvc_version_):
    global mvc_version
    mvc_version = mvc_version_

def get_license_data():
    # set default value
    sentinel_machradar_pro_status = 1 #Sentinel認證狀態#-1:未認證,0:認證成功,1:認證失敗

    sentinel_machradar_pro_status = libc.hasp_login(fid, c_char_p(sentinel_vcode.encode('utf-8')), byref(sentinel_handle))   #status == 0表示認證OK
    MachRadar_VerP_authorize, MachRadar_VerP_authorize_info = get_version_date(sentinel_vcode, fid, libc, sentinel_handle)
    logger.debug(f"MachRadar_VerP_authorize: {MachRadar_VerP_authorize}, MachRadar_VerP_authorize_info: {MachRadar_VerP_authorize_info}, sentinel_machradar_pro_status: {sentinel_machradar_pro_status}")
    return MachRadar_VerP_authorize, MachRadar_VerP_authorize_info, sentinel_machradar_pro_status

def get_version_date(sentinel_vcode, fid, libc, sentinel_handle):
    sentinel_vcode_ = sentinel_vcode
    fid_ = fid
    sentinel_handle_ = sentinel_handle
    libc_ = libc

    # set default value
    global MachRadar_VerP_authorize,MachRadar_VerP_authorize_info
    MachRadar_VerP_authorize = 0 #MachRadar_S授權類型 0:未授權,1:永久授權,2:有限期授權
    MachRadar_VerP_authorize_info = "未授權"

    status_MachRadar_VerP = libc_.hasp_login(fid_, c_char_p(sentinel_vcode_.encode('utf-8')), byref(sentinel_handle_))
    if status_MachRadar_VerP ==0:
        scope = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>" \
        "<haspscope/>";
            
        format = "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>" \
        "<haspformat root=\"hasp_info\">" \
        "    <feature>" \
        "        <attribute name=\"id\" />" \
        "        <element name=\"license\" />" \
        "        <hasp>" \
        "          <attribute name=\"id\" />" \
        "          <attribute name=\"type\" />" \
        "        </hasp>" \
        "    </feature>" \
        "</haspformat>" \
        ""   
        info = POINTER(c_char)()
        info_s = ""
        status = libc_.hasp_get_info(c_char_p(scope.encode('utf-8')), c_char_p(format.encode('utf-8')), c_char_p(sentinel_vcode_.encode('utf-8')), byref(info))

        if status != 0:
            return None  # 根據您的需求，處理錯誤狀況

        for i in range(10240):
            if info[i] != b'\000':
                info_s += info[i].decode('utf-8')
            else:
                break
                
        tree = ET.fromstring(info_s)
        for feature in tree.findall('feature'):
            if feature.attrib['id'] == str(fid):
                license_type = feature.find('license/license_type').text
                if license_type == "perpetual":
                    MachRadar_VerP_authorize = 1
                    MachRadar_VerP_authorize_info = "永久授權"
                    
                elif license_type == "expiration":
                    exp_date = feature.find('license/exp_date').text
                    timeArray = time.localtime(int(exp_date))
                    Time = time.strftime("%Y/%m/%d", timeArray)
                    MachRadar_VerP_authorize_info = Time
                    MachRadar_VerP_authorize = 2

    return MachRadar_VerP_authorize, MachRadar_VerP_authorize_info